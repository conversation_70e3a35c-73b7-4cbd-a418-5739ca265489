#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
诊断数据准备过程中的问题
检查因子数据与价格数据的时间对齐和分位数计算
"""

import sqlite3
import pandas as pd
import numpy as np
import alphalens as al
from scipy.stats import pearsonr
import warnings
warnings.filterwarnings('ignore')

def load_data_step_by_step():
    """逐步加载数据并检查每一步"""
    print("🔍 逐步诊断数据加载过程...")
    print("="*60)
    
    # 1. 加载因子数据
    print("1️⃣ 加载因子数据...")
    conn = sqlite3.connect('ganggutong_factor_data.db')
    
    # 检查表结构
    tables = pd.read_sql_query("SELECT name FROM sqlite_master WHERE type='table'", conn)
    print(f"   可用表: {list(tables['name'])}")
    
    # 使用正确的表名
    if 'volatility_12m_factor' in tables['name'].values:
        table_name = 'volatility_12m_factor'
    else:
        table_name = 'volatility_factor'
    
    factor_query = f"""
        SELECT stock_code, date, volatility_12m
        FROM {table_name}
        WHERE date >= '2024-01-01' AND date <= '2024-01-31'
        AND volatility_12m IS NOT NULL
        ORDER BY date, stock_code
    """
    factor_data = pd.read_sql_query(factor_query, conn)
    conn.close()
    
    print(f"   ✅ 因子数据: {len(factor_data)} 条")
    print(f"   📊 日期范围: {factor_data['date'].min()} 到 {factor_data['date'].max()}")
    print(f"   📊 股票数量: {factor_data['stock_code'].nunique()}")
    print(f"   📊 因子统计: 均值={factor_data['volatility_12m'].mean():.4f}, 标准差={factor_data['volatility_12m'].std():.4f}")
    
    # 2. 加载价格数据
    print("\n2️⃣ 加载价格数据...")
    stock_codes = factor_data['stock_code'].unique()
    stock_codes_str = "','".join(stock_codes)
    
    conn = sqlite3.connect('ganggutong_10year_data.db')
    price_query = f"""
        SELECT stock_code, date, close
        FROM stock_prices
        WHERE stock_code IN ('{stock_codes_str}')
        AND date >= '2024-01-01' AND date <= '2024-02-29'
        ORDER BY date, stock_code
    """
    price_data = pd.read_sql_query(price_query, conn)
    conn.close()
    
    print(f"   ✅ 价格数据: {len(price_data)} 条")
    print(f"   📊 日期范围: {price_data['date'].min()} 到 {price_data['date'].max()}")
    print(f"   📊 股票数量: {price_data['stock_code'].nunique()}")
    
    return factor_data, price_data

def diagnose_data_preparation(factor_data, price_data):
    """诊断数据准备过程"""
    print("\n🔍 诊断数据准备过程...")
    print("="*60)
    
    # 1. 检查原始数据
    print("1️⃣ 原始数据检查:")
    print(f"   因子数据形状: {factor_data.shape}")
    print(f"   价格数据形状: {price_data.shape}")
    
    # 检查日期格式
    print(f"   因子数据日期类型: {type(factor_data['date'].iloc[0])}")
    print(f"   价格数据日期类型: {type(price_data['date'].iloc[0])}")
    
    # 2. 转换日期格式
    print("\n2️⃣ 日期格式转换:")
    factor_data['date'] = pd.to_datetime(factor_data['date'])
    price_data['date'] = pd.to_datetime(price_data['date'])
    
    print(f"   转换后因子数据日期类型: {type(factor_data['date'].iloc[0])}")
    print(f"   转换后价格数据日期类型: {type(price_data['date'].iloc[0])}")
    
    # 3. 检查时间对齐
    print("\n3️⃣ 时间对齐检查:")
    factor_dates = set(factor_data['date'].unique())
    price_dates = set(price_data['date'].unique())
    
    common_dates = factor_dates.intersection(price_dates)
    factor_only_dates = factor_dates - price_dates
    price_only_dates = price_dates - factor_dates
    
    print(f"   因子数据日期数: {len(factor_dates)}")
    print(f"   价格数据日期数: {len(price_dates)}")
    print(f"   共同日期数: {len(common_dates)}")
    print(f"   仅因子数据日期数: {len(factor_only_dates)}")
    print(f"   仅价格数据日期数: {len(price_only_dates)}")
    
    if factor_only_dates:
        print(f"   ⚠️  仅因子数据的日期: {sorted(list(factor_only_dates))[:5]}...")
    if price_only_dates:
        print(f"   ⚠️  仅价格数据的日期: {sorted(list(price_only_dates))[:5]}...")
    
    # 4. 检查股票对齐
    print("\n4️⃣ 股票对齐检查:")
    factor_stocks = set(factor_data['stock_code'].unique())
    price_stocks = set(price_data['stock_code'].unique())
    
    common_stocks = factor_stocks.intersection(price_stocks)
    factor_only_stocks = factor_stocks - price_stocks
    price_only_stocks = price_stocks - factor_stocks
    
    print(f"   因子数据股票数: {len(factor_stocks)}")
    print(f"   价格数据股票数: {len(price_stocks)}")
    print(f"   共同股票数: {len(common_stocks)}")
    print(f"   仅因子数据股票数: {len(factor_only_stocks)}")
    print(f"   仅价格数据股票数: {len(price_only_stocks)}")
    
    return factor_data, price_data, common_dates, common_stocks

def diagnose_alphalens_preparation(factor_data, price_data, common_dates, common_stocks):
    """诊断alphalens数据准备过程"""
    print("\n🔍 诊断alphalens数据准备...")
    print("="*60)
    
    # 1. 创建价格透视表
    print("1️⃣ 创建价格透视表:")
    prices = price_data.pivot(index='date', columns='stock_code', values='close')
    prices = prices.sort_index()
    
    print(f"   价格透视表形状: {prices.shape}")
    print(f"   价格数据日期范围: {prices.index.min()} 到 {prices.index.max()}")
    print(f"   价格数据股票数: {len(prices.columns)}")
    
    # 2. 创建因子透视表
    print("\n2️⃣ 创建因子透视表:")
    factor_pivot = factor_data.pivot(index='date', columns='stock_code', values='volatility_12m')
    factor_pivot = factor_pivot.sort_index()
    
    print(f"   因子透视表形状: {factor_pivot.shape}")
    print(f"   因子数据日期范围: {factor_pivot.index.min()} 到 {factor_pivot.index.max()}")
    print(f"   因子数据股票数: {len(factor_pivot.columns)}")
    
    # 3. 转换为alphalens格式
    print("\n3️⃣ 转换为alphalens格式:")
    factor_series = factor_pivot.stack()
    factor_series.index.names = ['date', 'asset']
    factor_series = factor_series.dropna()
    
    print(f"   因子Series长度: {len(factor_series)}")
    print(f"   因子Series索引层级: {factor_series.index.names}")
    print(f"   因子Series样本: {factor_series.head()}")
    
    # 4. 检查数据对齐
    print("\n4️⃣ 检查数据对齐:")
    
    # 获取共同的日期和股票
    factor_dates_pivot = set(factor_pivot.index)
    price_dates_pivot = set(prices.index)
    common_dates_pivot = factor_dates_pivot.intersection(price_dates_pivot)
    
    factor_stocks_pivot = set(factor_pivot.columns)
    price_stocks_pivot = set(prices.columns)
    common_stocks_pivot = factor_stocks_pivot.intersection(price_stocks_pivot)
    
    print(f"   透视表共同日期数: {len(common_dates_pivot)}")
    print(f"   透视表共同股票数: {len(common_stocks_pivot)}")
    
    # 5. 手工验证一个日期的数据
    print("\n5️⃣ 手工验证样本日期:")
    sample_date = sorted(list(common_dates_pivot))[0]
    print(f"   样本日期: {sample_date}")
    
    # 获取该日期的因子和价格数据
    factor_day = factor_pivot.loc[sample_date].dropna()
    price_day = prices.loc[sample_date].dropna()
    
    # 找到共同股票
    common_stocks_day = set(factor_day.index).intersection(set(price_day.index))
    print(f"   该日期共同股票数: {len(common_stocks_day)}")
    
    if len(common_stocks_day) >= 10:
        # 计算下一日收益率
        next_date_idx = prices.index.get_loc(sample_date) + 1
        if next_date_idx < len(prices.index):
            next_date = prices.index[next_date_idx]
            price_next = prices.loc[next_date].dropna()
            
            # 计算收益率
            returns_manual = {}
            for stock in common_stocks_day:
                if stock in price_next.index:
                    ret = price_next[stock] / price_day[stock] - 1
                    returns_manual[stock] = ret
            
            print(f"   手工计算收益率数: {len(returns_manual)}")
            
            if len(returns_manual) >= 10:
                # 构建数据进行分析
                analysis_data = []
                for stock in returns_manual.keys():
                    analysis_data.append({
                        'stock': stock,
                        'factor': factor_day[stock],
                        'return': returns_manual[stock]
                    })
                
                analysis_df = pd.DataFrame(analysis_data)
                
                # 计算IC
                ic_manual = analysis_df['factor'].corr(analysis_df['return'])
                print(f"   手工IC: {ic_manual:.4f}")
                
                # 计算分位数
                analysis_df_sorted = analysis_df.sort_values('factor')
                n = len(analysis_df_sorted)
                
                q1_stocks = analysis_df_sorted.iloc[:n//5]
                q5_stocks = analysis_df_sorted.iloc[4*n//5:]
                
                q1_return = q1_stocks['return'].mean()
                q5_return = q5_stocks['return'].mean()
                
                print(f"   手工Q1收益: {q1_return:.4f}")
                print(f"   手工Q5收益: {q5_return:.4f}")
                print(f"   逻辑一致性: {'✅' if (ic_manual < 0 and q1_return > q5_return) or (ic_manual > 0 and q1_return < q5_return) else '❌'}")
    
    return factor_series, prices

def run_alphalens_and_compare(factor_series, prices):
    """运行alphalens并对比结果"""
    print("\n🔍 运行alphalens并对比...")
    print("="*60)
    
    try:
        # 运行alphalens
        factor_data_clean = al.utils.get_clean_factor_and_forward_returns(
            factor=factor_series,
            prices=prices,
            periods=(1,),
            quantiles=5,
            max_loss=0.35
        )
        
        print(f"   ✅ alphalens数据: {len(factor_data_clean)} 条")
        
        # 计算IC
        ic = al.performance.factor_information_coefficient(factor_data_clean)
        ic_mean = ic['1D'].mean()
        
        # 计算分位数收益
        mean_return_by_q, _ = al.performance.mean_return_by_quantile(factor_data_clean)
        q1_return = mean_return_by_q.loc[1, '1D']
        q5_return = mean_return_by_q.loc[5, '1D']
        
        print(f"   📊 alphalens IC: {ic_mean:.4f}")
        print(f"   📊 alphalens Q1收益: {q1_return:.4f}")
        print(f"   📊 alphalens Q5收益: {q5_return:.4f}")
        print(f"   📊 逻辑一致性: {'✅' if (ic_mean < 0 and q1_return > q5_return) or (ic_mean > 0 and q1_return < q5_return) else '❌'}")
        
        # 检查alphalens内部数据
        print(f"\n   🔍 alphalens内部数据检查:")
        print(f"   数据列: {list(factor_data_clean.columns)}")
        print(f"   样本数据:")
        print(factor_data_clean.head())
        
    except Exception as e:
        print(f"   ❌ alphalens失败: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主函数"""
    print("🎯 诊断数据准备过程")
    print("="*50)
    
    # 1. 逐步加载数据
    factor_data, price_data = load_data_step_by_step()
    
    # 2. 诊断数据准备
    factor_data, price_data, common_dates, common_stocks = diagnose_data_preparation(factor_data, price_data)
    
    # 3. 诊断alphalens准备
    factor_series, prices = diagnose_alphalens_preparation(factor_data, price_data, common_dates, common_stocks)
    
    # 4. 运行alphalens并对比
    run_alphalens_and_compare(factor_series, prices)

if __name__ == "__main__":
    main()
