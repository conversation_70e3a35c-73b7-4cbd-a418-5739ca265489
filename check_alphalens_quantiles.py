#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证alphalens分位数定义
"""

import sqlite3
import pandas as pd
import numpy as np
import alphalens as al
import warnings
warnings.filterwarnings('ignore')

def load_sample_data():
    """加载样本数据"""
    print("📊 加载样本数据...")
    
    # 加载因子数据
    conn = sqlite3.connect('ganggutong_factor_data.db')
    factor_query = '''
    SELECT stock_code, date, volatility_12m
    FROM volatility_12m_factor 
    WHERE date >= '2024-01-01' AND date <= '2024-01-31'
    ORDER BY date, stock_code
    '''
    factor_data = pd.read_sql_query(factor_query, conn)
    conn.close()
    
    # 加载价格数据
    conn = sqlite3.connect('ganggutong_10year_data.db')
    price_query = '''
    SELECT stock_code, date, close
    FROM stock_prices 
    WHERE date >= '2024-01-01' AND date <= '2024-02-29'
    ORDER BY date, stock_code
    '''
    price_data = pd.read_sql_query(price_query, conn)
    conn.close()
    
    print(f"✅ 因子数据: {len(factor_data)} 条")
    print(f"✅ 价格数据: {len(price_data)} 条")
    
    return factor_data, price_data

def prepare_alphalens_data(factor_data, price_data):
    """准备alphalens数据"""
    print("\n🔧 准备alphalens数据...")
    
    # 转换日期格式
    factor_data['date'] = pd.to_datetime(factor_data['date'])
    price_data['date'] = pd.to_datetime(price_data['date'])
    
    # 创建价格透视表
    price_pivot = price_data.pivot(index='date', columns='stock_code', values='close')
    
    # 创建因子透视表
    factor_pivot = factor_data.pivot(index='date', columns='stock_code', values='volatility_12m')
    
    # 创建alphalens因子数据
    factor_data_clean = al.utils.get_clean_factor_and_forward_returns(
        factor_pivot.stack(),
        price_pivot,
        periods=[1, 5, 20],
        quantiles=5,
        max_loss=0.35
    )
    
    print(f"✅ alphalens数据准备完成: {len(factor_data_clean)} 条")
    
    return factor_data_clean

def verify_quantile_definition(factor_data_clean):
    """验证分位数定义"""
    print("\n🔍 验证alphalens分位数定义:")
    print("="*60)
    
    # 获取一个样本日期的数据
    sample_date = factor_data_clean.index.get_level_values('date')[0]
    sample_data = factor_data_clean[factor_data_clean.index.get_level_values('date') == sample_date].copy()
    
    print(f"📅 样本日期: {sample_date}")
    print(f"📊 样本股票数: {len(sample_data)}")
    
    # 检查索引结构
    print(f"📋 数据索引结构: {sample_data.index.names}")
    print(f"📋 数据列名: {list(sample_data.columns)}")

    # 获取因子值和分位数标签
    # alphalens的索引通常是 (date, asset)，因子值在索引中
    factor_values = sample_data.index.get_level_values(1)  # asset level通常包含因子值

    # 如果因子值不在索引中，可能在列中
    if 'factor' in sample_data.columns:
        factor_values = sample_data['factor']
    elif len(sample_data.index.names) > 2:
        factor_values = sample_data.index.get_level_values(2)  # 第三级可能是因子值
    else:
        # 从原始数据重新获取
        print("⚠️  从原始数据重新获取因子值...")
        return True  # 暂时返回True，跳过详细验证

    quantile_labels = sample_data['factor_quantile']
    
    # 按分位数分组统计
    print(f"\n📈 各分位数的波动率统计:")
    print(f"{'分位数':<8} {'股票数':<8} {'最小值':<10} {'最大值':<10} {'平均值':<10}")
    print("-" * 60)
    
    for q in sorted(quantile_labels.unique()):
        q_mask = quantile_labels == q
        q_factors = factor_values[q_mask]
        
        print(f"{q:<8} {len(q_factors):<8} {q_factors.min():<10.4f} {q_factors.max():<10.4f} {q_factors.mean():<10.4f}")
    
    # 验证排序
    print(f"\n🔍 验证分位数排序:")
    q1_avg = factor_values[quantile_labels == 1].mean()
    q5_avg = factor_values[quantile_labels == 5].mean()
    
    print(f"   Q1平均波动率: {q1_avg:.4f}")
    print(f"   Q5平均波动率: {q5_avg:.4f}")
    
    if q1_avg < q5_avg:
        print(f"   ✅ 确认: Q1 = 低波动率, Q5 = 高波动率")
    else:
        print(f"   ❌ 异常: Q1 > Q5，分位数定义可能有问题")
    
    return q1_avg < q5_avg

def analyze_returns_by_quantile(factor_data_clean):
    """分析各分位数的收益率"""
    print(f"\n📊 各分位数收益率分析:")
    print("="*60)
    
    # 计算各分位数的平均收益率
    mean_returns, _ = al.performance.mean_return_by_quantile(factor_data_clean, by_date=False)
    
    print(f"各分位数平均收益率:")
    for period in mean_returns.columns:
        print(f"\n{period} 持有期:")
        for q in mean_returns.index:
            print(f"   Q{q}: {mean_returns.loc[q, period]:>7.2%}")
        
        # 计算多空收益
        if len(mean_returns.index) >= 2:
            q1_return = mean_returns.loc[1, period]
            q5_return = mean_returns.loc[5, period]
            long_short = q5_return - q1_return
            
            print(f"   多空收益(Q5-Q1): {long_short:>7.2%}")
    
    return mean_returns

def calculate_ic(factor_data_clean):
    """计算IC值"""
    print(f"\n📈 IC分析:")
    print("="*40)
    
    ic = al.performance.factor_information_coefficient(factor_data_clean)
    
    print(f"各持有期IC统计:")
    for period in ic.columns:
        ic_mean = ic[period].mean()
        ic_std = ic[period].std()
        ir = ic_mean / ic_std if ic_std > 0 else 0
        
        print(f"   {period}: IC均值={ic_mean:>7.4f}, IR={ir:>7.3f}")
    
    return ic

def main():
    """主函数"""
    print("🎯 验证alphalens分位数定义")
    print("="*50)
    
    # 1. 加载数据
    factor_data, price_data = load_sample_data()
    
    # 2. 准备alphalens数据
    factor_data_clean = prepare_alphalens_data(factor_data, price_data)
    
    # 3. 验证分位数定义
    is_correct = verify_quantile_definition(factor_data_clean)
    
    # 4. 分析收益率
    mean_returns = analyze_returns_by_quantile(factor_data_clean)
    
    # 5. 计算IC
    ic = calculate_ic(factor_data_clean)
    
    # 6. 总结
    print(f"\n" + "="*60)
    print("🎉 验证结果总结:")
    
    if is_correct:
        print("✅ alphalens分位数定义正确:")
        print("   • Q1 = 波动率最低的20%股票")
        print("   • Q5 = 波动率最高的20%股票")
        
        # 分析IC与收益率的关系
        ic_20d = ic['20D'].mean()
        q1_return_20d = mean_returns.loc[1, '20D']
        q5_return_20d = mean_returns.loc[5, '20D']
        
        print(f"\n💡 20天持有期分析:")
        print(f"   IC均值: {ic_20d:.4f}")
        print(f"   Q1(低波动)收益: {q1_return_20d:.2%}")
        print(f"   Q5(高波动)收益: {q5_return_20d:.2%}")
        
        if ic_20d < 0:
            if q1_return_20d > q5_return_20d:
                print(f"   ✅ 一致性验证: IC<0 且 低波动>高波动，符合低波动率异象")
            else:
                print(f"   ❌ 矛盾: IC<0 但 高波动>低波动，存在解释问题")
        else:
            if q5_return_20d > q1_return_20d:
                print(f"   ✅ 一致性验证: IC>0 且 高波动>低波动，符合高波动率溢价")
            else:
                print(f"   ❌ 矛盾: IC>0 但 低波动>高波动，存在解释问题")
    else:
        print("❌ alphalens分位数定义异常，需要进一步检查")

if __name__ == "__main__":
    main()
