#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
手工验证腾讯12月波动率计算
"""

import sqlite3
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

def load_tencent_data():
    """加载腾讯价格数据"""
    print("📊 加载腾讯价格数据...")
    
    conn = sqlite3.connect('ganggutong_10year_data.db')
    
    # 获取腾讯价格数据
    query = '''
    SELECT stock_code, date, close
    FROM stock_prices 
    WHERE stock_code = '00700'
    AND date >= '2023-01-01'
    ORDER BY date
    '''
    
    price_data = pd.read_sql_query(query, conn)
    conn.close()
    
    price_data['date'] = pd.to_datetime(price_data['date'])
    price_data = price_data.sort_values('date')
    
    print(f"✅ 腾讯价格数据: {len(price_data)} 条")
    print(f"   时间范围: {price_data['date'].min()} 到 {price_data['date'].max()}")
    
    return price_data

def calculate_manual_volatility(price_data, window=252):
    """手工计算12月滚动波动率"""
    print(f"\n🔧 手工计算12月滚动波动率 (窗口={window}天)...")
    
    # 计算日收益率
    price_data['log_return'] = np.log(price_data['close'] / price_data['close'].shift(1))
    
    # 计算滚动波动率
    price_data['volatility_12m_manual'] = price_data['log_return'].rolling(
        window=window, min_periods=int(window*0.8)
    ).std() * np.sqrt(252)  # 年化
    
    # 显示计算过程示例
    print("\n📋 计算过程示例 (最近10个交易日):")
    recent_data = price_data.tail(15)[['date', 'close', 'log_return', 'volatility_12m_manual']].copy()
    recent_data['log_return'] = recent_data['log_return'].round(6)
    recent_data['volatility_12m_manual'] = recent_data['volatility_12m_manual'].round(4)
    
    print(recent_data.to_string(index=False))
    
    return price_data

def load_database_volatility():
    """加载数据库中的波动率数据"""
    print(f"\n📊 加载数据库中的波动率数据...")
    
    conn = sqlite3.connect('ganggutong_factor_data.db')
    
    query = '''
    SELECT stock_code, date, volatility_12m
    FROM volatility_12m_factor 
    WHERE stock_code = '00700'
    AND date >= '2023-01-01'
    ORDER BY date
    '''
    
    factor_data = pd.read_sql_query(query, conn)
    conn.close()
    
    factor_data['date'] = pd.to_datetime(factor_data['date'])
    
    print(f"✅ 数据库波动率数据: {len(factor_data)} 条")
    print(f"   时间范围: {factor_data['date'].min()} 到 {factor_data['date'].max()}")
    
    return factor_data

def compare_volatility(manual_data, database_data):
    """对比手工计算和数据库中的波动率"""
    print(f"\n🔍 对比手工计算和数据库波动率...")
    
    # 合并数据
    manual_vol = manual_data[['date', 'volatility_12m_manual']].copy()
    database_vol = database_data[['date', 'volatility_12m']].copy()
    
    merged = pd.merge(manual_vol, database_vol, on='date', how='inner')
    merged = merged.dropna()
    
    if len(merged) == 0:
        print("❌ 没有找到匹配的日期数据")
        return
    
    print(f"✅ 匹配数据: {len(merged)} 条")
    
    # 计算差异
    merged['difference'] = merged['volatility_12m_manual'] - merged['volatility_12m']
    merged['relative_diff'] = merged['difference'] / merged['volatility_12m'] * 100
    
    # 显示对比结果
    print(f"\n📊 波动率对比结果:")
    print("="*80)
    
    comparison_data = merged.tail(10)[['date', 'volatility_12m_manual', 'volatility_12m', 'difference', 'relative_diff']].copy()
    comparison_data['volatility_12m_manual'] = comparison_data['volatility_12m_manual'].round(4)
    comparison_data['volatility_12m'] = comparison_data['volatility_12m'].round(4)
    comparison_data['difference'] = comparison_data['difference'].round(6)
    comparison_data['relative_diff'] = comparison_data['relative_diff'].round(2)
    
    print("最近10个交易日对比:")
    print(comparison_data.to_string(index=False))
    
    # 统计分析
    print(f"\n📈 统计分析:")
    print(f"   平均绝对差异: {abs(merged['difference']).mean():.6f}")
    print(f"   最大绝对差异: {abs(merged['difference']).max():.6f}")
    print(f"   平均相对差异: {abs(merged['relative_diff']).mean():.2f}%")
    print(f"   最大相对差异: {abs(merged['relative_diff']).max():.2f}%")
    
    # 相关性分析
    correlation = merged['volatility_12m_manual'].corr(merged['volatility_12m'])
    print(f"   相关系数: {correlation:.6f}")
    
    # 判断是否一致
    if abs(merged['relative_diff']).mean() < 1.0:  # 平均相对差异小于1%
        print(f"\n✅ 结论: 手工计算与数据库数据高度一致!")
    elif abs(merged['relative_diff']).mean() < 5.0:  # 平均相对差异小于5%
        print(f"\n⚠️  结论: 手工计算与数据库数据基本一致，存在小幅差异")
    else:
        print(f"\n❌ 结论: 手工计算与数据库数据存在显著差异，需要检查计算方法")
    
    return merged

def detailed_calculation_check(price_data, target_date='2024-06-30'):
    """详细检查特定日期的计算过程"""
    print(f"\n🔍 详细检查 {target_date} 的计算过程...")
    
    target_date = pd.to_datetime(target_date)
    
    # 找到目标日期
    target_idx = price_data[price_data['date'] == target_date].index
    if len(target_idx) == 0:
        print(f"❌ 没有找到 {target_date} 的数据")
        return
    
    target_idx = target_idx[0]
    
    # 获取过去252个交易日的数据
    start_idx = max(0, target_idx - 251)
    window_data = price_data.iloc[start_idx:target_idx+1].copy()
    
    print(f"   计算窗口: {window_data['date'].min()} 到 {window_data['date'].max()}")
    print(f"   数据点数: {len(window_data)}")
    
    # 重新计算收益率
    window_data['log_return'] = np.log(window_data['close'] / window_data['close'].shift(1))
    returns = window_data['log_return'].dropna()
    
    print(f"   有效收益率数据: {len(returns)} 个")
    print(f"   收益率均值: {returns.mean():.6f}")
    print(f"   收益率标准差: {returns.std():.6f}")
    
    # 计算年化波动率
    annualized_vol = returns.std() * np.sqrt(252)
    print(f"   年化波动率: {annualized_vol:.4f}")
    
    # 显示最近几天的收益率
    print(f"\n   最近5天收益率:")
    recent_returns = window_data.tail(6)[['date', 'close', 'log_return']]
    recent_returns['log_return'] = recent_returns['log_return'].round(6)
    print(recent_returns.to_string(index=False))
    
    return annualized_vol

def main():
    """主函数"""
    print("🎯 腾讯12月波动率手工验证")
    print("="*60)
    
    # 1. 加载腾讯价格数据
    price_data = load_tencent_data()
    
    # 2. 手工计算波动率
    price_data = calculate_manual_volatility(price_data)
    
    # 3. 加载数据库波动率
    database_data = load_database_volatility()
    
    # 4. 对比结果
    comparison = compare_volatility(price_data, database_data)
    
    # 5. 详细检查特定日期
    if comparison is not None and len(comparison) > 0:
        latest_date = comparison['date'].max().strftime('%Y-%m-%d')
        detailed_calculation_check(price_data, latest_date)
    
    print(f"\n" + "="*80)
    print("🎉 腾讯波动率验证完成!")

if __name__ == "__main__":
    main()
