#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
手工验证分位数收益计算
独立于alphalens验证IC与分位数收益的一致性
"""

import sqlite3
import pandas as pd
import numpy as np
from scipy.stats import pearsonr
import warnings
warnings.filterwarnings('ignore')

def load_data():
    """加载数据"""
    print("📊 加载数据...")
    
    # 加载因子数据
    conn = sqlite3.connect('ganggutong_factor_data.db')
    factor_query = '''
    SELECT stock_code, date, volatility_12m
    FROM volatility_12m_factor 
    WHERE date >= '2024-01-01' AND date <= '2024-01-31'
    ORDER BY date, stock_code
    '''
    factor_data = pd.read_sql_query(factor_query, conn)
    conn.close()
    
    # 加载价格数据
    conn = sqlite3.connect('ganggutong_10year_data.db')
    price_query = '''
    SELECT stock_code, date, close
    FROM stock_prices 
    WHERE date >= '2024-01-01' AND date <= '2024-02-29'
    ORDER BY date, stock_code
    '''
    price_data = pd.read_sql_query(price_query, conn)
    conn.close()
    
    print(f"✅ 因子数据: {len(factor_data)} 条")
    print(f"✅ 价格数据: {len(price_data)} 条")
    
    return factor_data, price_data

def calculate_forward_returns(price_data, periods=[1, 5, 20]):
    """计算前向收益率"""
    print(f"\n🔧 计算前向收益率...")
    
    price_data['date'] = pd.to_datetime(price_data['date'])
    price_data = price_data.sort_values(['stock_code', 'date'])
    
    forward_returns = {}
    
    for period in periods:
        print(f"   计算 {period} 天前向收益...")
        
        returns_list = []
        
        for stock in price_data['stock_code'].unique():
            stock_prices = price_data[price_data['stock_code'] == stock].copy()
            stock_prices = stock_prices.sort_values('date')
            
            # 计算前向收益率
            stock_prices[f'return_{period}d'] = stock_prices['close'].shift(-period) / stock_prices['close'] - 1
            
            # 只保留有效的收益率数据
            valid_returns = stock_prices[['stock_code', 'date', f'return_{period}d']].dropna()
            returns_list.append(valid_returns)
        
        if returns_list:
            forward_returns[period] = pd.concat(returns_list, ignore_index=True)
            print(f"   ✅ {period}天收益率: {len(forward_returns[period])} 条")
    
    return forward_returns

def manual_quantile_analysis(factor_data, forward_returns, period=1):
    """手工分位数分析"""
    print(f"\n🔍 手工验证 {period} 天分位数收益...")
    print("="*60)
    
    # 转换日期格式
    factor_data['date'] = pd.to_datetime(factor_data['date'])
    forward_returns[period]['date'] = pd.to_datetime(forward_returns[period]['date'])
    
    # 合并数据
    merged_data = pd.merge(
        factor_data,
        forward_returns[period],
        on=['date', 'stock_code'],
        how='inner'
    )
    
    print(f"📊 合并后数据: {len(merged_data)} 条")
    
    if len(merged_data) == 0:
        print("❌ 没有合并数据")
        return None
    
    # 按日期分组分析
    daily_results = []
    
    for date, group in merged_data.groupby('date'):
        if len(group) < 50:  # 至少需要50只股票
            continue
        
        # 手工计算分位数
        group_sorted = group.sort_values('volatility_12m')
        n = len(group_sorted)
        
        # 计算5分位数
        q1_end = n // 5
        q2_end = 2 * n // 5
        q3_end = 3 * n // 5
        q4_end = 4 * n // 5
        
        quantiles = {
            'Q1': group_sorted.iloc[:q1_end],
            'Q2': group_sorted.iloc[q1_end:q2_end],
            'Q3': group_sorted.iloc[q2_end:q3_end],
            'Q4': group_sorted.iloc[q3_end:q4_end],
            'Q5': group_sorted.iloc[q4_end:]
        }
        
        # 计算各分位数的平均收益率
        quantile_returns = {}
        quantile_volatilities = {}
        
        for q_name, q_data in quantiles.items():
            if len(q_data) > 0:
                quantile_returns[q_name] = q_data[f'return_{period}d'].mean()
                quantile_volatilities[q_name] = q_data['volatility_12m'].mean()
        
        # 计算IC (手工计算)
        ic_manual = group['volatility_12m'].corr(group[f'return_{period}d'])
        
        # 使用scipy验证IC计算
        ic_scipy, p_value = pearsonr(group['volatility_12m'], group[f'return_{period}d'])
        
        daily_result = {
            'date': date,
            'stock_count': len(group),
            'ic_manual': ic_manual,
            'ic_scipy': ic_scipy,
            'ic_p_value': p_value,
            **{f'{q}_return': quantile_returns.get(q, np.nan) for q in ['Q1', 'Q2', 'Q3', 'Q4', 'Q5']},
            **{f'{q}_volatility': quantile_volatilities.get(q, np.nan) for q in ['Q1', 'Q2', 'Q3', 'Q4', 'Q5']}
        }
        
        daily_results.append(daily_result)
    
    if not daily_results:
        print("❌ 没有有效的日期数据")
        return None
    
    # 转换为DataFrame
    results_df = pd.DataFrame(daily_results)
    
    return results_df

def analyze_results(results_df, period=1):
    """分析结果"""
    print(f"\n📈 {period}天分位数分析结果:")
    print("="*60)
    
    # IC统计
    ic_mean = results_df['ic_manual'].mean()
    ic_std = results_df['ic_manual'].std()
    ic_ir = ic_mean / ic_std if ic_std > 0 else 0
    ic_positive_rate = (results_df['ic_manual'] > 0).mean()
    
    print(f"📊 IC分析:")
    print(f"   IC均值: {ic_mean:>8.4f}")
    print(f"   IC标准差: {ic_std:>6.4f}")
    print(f"   IR: {ic_ir:>11.3f}")
    print(f"   IC>0比例: {ic_positive_rate:>5.1%}")
    
    # 分位数收益率统计
    print(f"\n📊 分位数收益率:")
    print(f"{'分位数':<8} {'平均收益':<10} {'平均波动率':<12} {'股票数':<8}")
    print("-" * 50)
    
    for q in ['Q1', 'Q2', 'Q3', 'Q4', 'Q5']:
        avg_return = results_df[f'{q}_return'].mean()
        avg_volatility = results_df[f'{q}_volatility'].mean()
        print(f"{q:<8} {avg_return:>9.4f} {avg_volatility:>11.4f}")
    
    # 验证逻辑一致性
    print(f"\n🔍 逻辑一致性验证:")
    q1_return = results_df['Q1_return'].mean()
    q5_return = results_df['Q5_return'].mean()
    q1_volatility = results_df['Q1_volatility'].mean()
    q5_volatility = results_df['Q5_volatility'].mean()
    
    print(f"   Q1(低波动): 收益={q1_return:>7.4f}, 波动率={q1_volatility:>7.4f}")
    print(f"   Q5(高波动): 收益={q5_return:>7.4f}, 波动率={q5_volatility:>7.4f}")
    print(f"   IC均值: {ic_mean:>7.4f}")
    
    # 检查一致性
    if ic_mean < 0:
        expected_relationship = "Q1收益 > Q5收益"
        actual_relationship = "Q1收益 > Q5收益" if q1_return > q5_return else "Q1收益 < Q5收益"
        consistent = q1_return > q5_return
    else:
        expected_relationship = "Q5收益 > Q1收益"
        actual_relationship = "Q5收益 > Q1收益" if q5_return > q1_return else "Q5收益 < Q1收益"
        consistent = q5_return > q1_return
    
    print(f"\n💡 一致性检查:")
    print(f"   IC<0预期: {expected_relationship}")
    print(f"   实际结果: {actual_relationship}")
    print(f"   逻辑一致: {'✅ 是' if consistent else '❌ 否'}")
    
    if not consistent:
        print(f"\n⚠️  发现矛盾！")
        print(f"   这表明数据处理或计算过程中存在错误")
    
    return {
        'ic_mean': ic_mean,
        'q1_return': q1_return,
        'q5_return': q5_return,
        'consistent': consistent
    }

def main():
    """主函数"""
    print("🎯 手工验证分位数收益计算")
    print("="*50)
    
    # 1. 加载数据
    factor_data, price_data = load_data()
    
    # 2. 计算前向收益率
    forward_returns = calculate_forward_returns(price_data, periods=[1, 5, 20])
    
    # 3. 手工分位数分析
    results = {}
    for period in [1, 5, 20]:
        if period in forward_returns:
            results_df = manual_quantile_analysis(factor_data, forward_returns, period)
            if results_df is not None:
                results[period] = analyze_results(results_df, period)
    
    # 4. 总结
    print(f"\n" + "="*60)
    print("🎉 验证结果总结:")
    
    for period, result in results.items():
        if result:
            status = "✅ 一致" if result['consistent'] else "❌ 矛盾"
            print(f"   {period}天: IC={result['ic_mean']:>7.4f}, Q1={result['q1_return']:>7.4f}, Q5={result['q5_return']:>7.4f} {status}")

if __name__ == "__main__":
    main()
