#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试alphalens的分位数定义
验证Q1是否是因子值最大的20%
"""

import pandas as pd
import numpy as np

def test_pandas_qcut():
    """测试pandas qcut的分位数定义"""
    print("🔍 测试pandas qcut的分位数定义")
    print("="*50)
    
    # 创建测试数据：1到100的数字
    data = np.arange(1, 101)
    print(f"📊 测试数据: {data[:10]}...{data[-10:]}")
    print(f"数据范围: {data.min()} - {data.max()}")
    
    # 使用qcut分成5组
    quantiles = pd.qcut(data, q=5, labels=['Q1', 'Q2', 'Q3', 'Q4', 'Q5'])
    
    # 分析每组的数据
    df = pd.DataFrame({'value': data, 'quantile': quantiles})
    
    print(f"\n📊 各分位数的数据范围:")
    for q in ['Q1', 'Q2', 'Q3', 'Q4', 'Q5']:
        group_data = df[df['quantile'] == q]['value']
        print(f"{q}: {group_data.min():>3} - {group_data.max():>3} (共{len(group_data)}个)")
    
    print(f"\n💡 结论:")
    print(f"Q1包含最小的20%数据 (1-20)")
    print(f"Q5包含最大的20%数据 (81-100)")
    print(f"所以Q1 = 因子值最小的20%，Q5 = 因子值最大的20%")

def test_alphalens_behavior():
    """测试alphalens的行为"""
    print(f"\n🔍 测试alphalens的分位数行为")
    print("="*50)
    
    try:
        import alphalens as al
        print(f"✅ alphalens版本: {al.__version__}")
        
        # 创建模拟数据
        dates = pd.date_range('2023-01-01', periods=5, freq='D')
        stocks = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J']
        
        # 创建因子数据 (MultiIndex: date, asset)
        factor_data = []
        for date in dates:
            for i, stock in enumerate(stocks):
                factor_data.append({
                    'date': date,
                    'asset': stock,
                    'factor': i + 1  # 因子值从1到10
                })
        
        factor_df = pd.DataFrame(factor_data)
        factor_series = factor_df.set_index(['date', 'asset'])['factor']
        
        # 创建价格数据
        np.random.seed(42)
        price_data = pd.DataFrame(
            np.random.randn(len(dates), len(stocks)) * 0.02 + 1,
            index=dates,
            columns=stocks
        ).cumprod()
        
        print(f"📊 因子数据样本:")
        print(factor_df.head(10))
        
        print(f"\n📊 价格数据样本:")
        print(price_data.head())
        
        # 使用alphalens处理
        factor_data_clean = al.utils.get_clean_factor_and_forward_returns(
            factor=factor_series,
            prices=price_data,
            periods=(1,),
            quantiles=5,
            max_loss=0.35
        )
        
        print(f"\n📊 alphalens处理后的数据:")
        print(factor_data_clean.head(10))
        
        # 分析分位数
        print(f"\n📊 各分位数的因子值范围:")
        for q in [1, 2, 3, 4, 5]:
            group_data = factor_data_clean[factor_data_clean['factor_quantile'] == q]['factor']
            if len(group_data) > 0:
                print(f"Q{q}: {group_data.min():.1f} - {group_data.max():.1f} (平均: {group_data.mean():.1f})")
        
        print(f"\n💡 alphalens结论:")
        print(f"Q1 = 因子值最小的分位数")
        print(f"Q5 = 因子值最大的分位数")
        
    except ImportError:
        print("❌ alphalens未安装，跳过测试")
    except Exception as e:
        print(f"❌ alphalens测试失败: {e}")

def test_volatility_factor_example():
    """用波动率因子举例说明"""
    print(f"\n🔍 波动率因子分位数含义")
    print("="*50)
    
    # 模拟波动率数据
    volatility_data = np.array([0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 1.0])
    stock_names = ['低波动A', '低波动B', '中波动C', '中波动D', '中波动E', 
                   '中波动F', '高波动G', '高波动H', '高波动I', '高波动J']
    
    # 分位数
    quantiles = pd.qcut(volatility_data, q=5, labels=['Q1', 'Q2', 'Q3', 'Q4', 'Q5'])
    
    df = pd.DataFrame({
        'stock': stock_names,
        'volatility': volatility_data,
        'quantile': quantiles
    })
    
    print(f"📊 波动率因子分位数示例:")
    print(df.to_string(index=False))
    
    print(f"\n💡 波动率因子分位数含义:")
    print(f"Q1 = 最低波动率的20%股票 (低波动率)")
    print(f"Q5 = 最高波动率的20%股票 (高波动率)")
    
    print(f"\n🎯 如果IC < 0 (低波动率异象):")
    print(f"预期: Q1收益 > Q5收益 (低波动率股票表现更好)")
    
    print(f"\n🎯 如果IC > 0 (高波动率溢价):")
    print(f"预期: Q5收益 > Q1收益 (高波动率股票表现更好)")

if __name__ == "__main__":
    test_pandas_qcut()
    test_alphalens_behavior()
    test_volatility_factor_example()
