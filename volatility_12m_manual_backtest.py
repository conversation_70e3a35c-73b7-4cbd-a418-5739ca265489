#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
12月波动率因子5分组手动回测分析
不使用alphalens，直接计算分组收益和IC
"""

import sqlite3
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

print("🎯 12月波动率因子手动分组回测分析")
print("="*60)

def load_data():
    """加载数据"""
    print("\n📊 加载数据...")
    
    # 1. 加载行业数据
    try:
        industry_df = pd.read_csv('data_files/ganggutong_consti.csv')
        industry_df['代码'] = industry_df['代码'].astype(str).str.zfill(5)
        print(f"✅ 行业数据: {len(industry_df)} 只股票, {industry_df['所属行业'].nunique()} 个行业")
    except Exception as e:
        print(f"❌ 无法加载行业数据: {str(e)}")
        return None, None, None
    
    # 2. 加载12月波动率因子数据
    conn_factor = sqlite3.connect('ganggutong_factor_data.db')
    
    # 从2015年开始分析
    factor_query = """
    SELECT stock_code, date, volatility_12m
    FROM volatility_12m_factor
    WHERE date >= '2015-01-01'
    AND volatility_12m IS NOT NULL
    ORDER BY date, stock_code
    """
    
    factor_data = pd.read_sql_query(factor_query, conn_factor)
    conn_factor.close()
    
    print(f"✅ 因子数据: {len(factor_data):,} 条")
    
    # 3. 加载价格数据
    conn_price = sqlite3.connect('ganggutong_10year_data.db')
    
    stock_codes = factor_data['stock_code'].unique()
    stock_codes_str = "','".join(stock_codes)
    
    price_query = f"""
    SELECT stock_code, date, close
    FROM stock_prices
    WHERE stock_code IN ('{stock_codes_str}')
    AND date >= '2015-01-01'
    ORDER BY date, stock_code
    """
    
    price_data = pd.read_sql_query(price_query, conn_price)
    conn_price.close()
    
    print(f"✅ 价格数据: {len(price_data):,} 条")
    
    return factor_data, price_data, industry_df

def calculate_forward_returns(price_data, periods=[20, 60, 120, 240]):
    """计算前向收益率"""
    print("\n🔧 计算前向收益率...")
    
    # 转换数据格式
    price_data['date'] = pd.to_datetime(price_data['date'])
    price_pivot = price_data.pivot(index='date', columns='stock_code', values='close')
    
    forward_returns = {}
    
    for period in periods:
        print(f"   计算 {period} 天前向收益率...")
        
        # 计算前向收益率
        future_prices = price_pivot.shift(-period)
        returns = (future_prices / price_pivot - 1)
        
        # 转换回长格式
        returns_long = returns.stack().reset_index()
        returns_long.columns = ['date', 'stock_code', f'return_{period}d']
        returns_long = returns_long.dropna()
        
        forward_returns[period] = returns_long
        print(f"      有效数据: {len(returns_long):,} 条")
    
    return forward_returns

def calculate_quantile_performance(factor_data, forward_returns, periods=[20, 60, 120, 240]):
    """计算分位数表现"""
    print("\n📈 计算分位数表现...")
    
    results = {}
    
    for period in periods:
        print(f"\n🔸 分析 {period} 天持有期:")
        
        # 合并因子数据和收益率数据
        returns_data = forward_returns[period]
        returns_data['date'] = pd.to_datetime(returns_data['date'])
        factor_data['date'] = pd.to_datetime(factor_data['date'])
        
        merged_data = pd.merge(
            factor_data, 
            returns_data, 
            on=['date', 'stock_code'], 
            how='inner'
        )
        
        print(f"   合并后数据: {len(merged_data):,} 条")
        
        if len(merged_data) == 0:
            continue
        
        # 按日期分组，计算每日的分位数
        daily_results = []
        
        for date, group in merged_data.groupby('date'):
            if len(group) < 50:  # 至少需要50只股票
                continue
            
            # 计算分位数
            group['quantile'] = pd.qcut(
                group['volatility_12m'], 
                q=5, 
                labels=[1, 2, 3, 4, 5],
                duplicates='drop'
            )
            
            # 计算各分位数的平均收益
            quantile_returns = group.groupby('quantile')[f'return_{period}d'].mean()
            
            # 计算IC
            ic = group['volatility_12m'].corr(group[f'return_{period}d'])
            
            daily_result = {
                'date': date,
                'ic': ic,
                'stock_count': len(group)
            }
            
            # 添加各分位数收益
            for q in [1, 2, 3, 4, 5]:
                if q in quantile_returns.index:
                    daily_result[f'Q{q}_return'] = quantile_returns[q]
                else:
                    daily_result[f'Q{q}_return'] = np.nan
            
            daily_results.append(daily_result)
        
        if not daily_results:
            continue
        
        # 转换为DataFrame
        daily_df = pd.DataFrame(daily_results)
        
        # 计算统计指标
        ic_mean = daily_df['ic'].mean()
        ic_std = daily_df['ic'].std()
        ic_ir = ic_mean / ic_std if ic_std > 0 else 0
        ic_positive_rate = (daily_df['ic'] > 0).mean()
        
        # 计算各分位数的平均收益
        quantile_stats = {}
        for q in [1, 2, 3, 4, 5]:
            col = f'Q{q}_return'
            if col in daily_df.columns:
                quantile_stats[f'Q{q}'] = {
                    'mean_return': daily_df[col].mean(),
                    'std_return': daily_df[col].std(),
                    'positive_rate': (daily_df[col] > 0).mean()
                }
        
        # 计算多空收益
        if 'Q1' in quantile_stats and 'Q5' in quantile_stats:
            # 基于IC方向确定多空策略
            if ic_mean < 0:  # 低波动率异象
                long_short_return = quantile_stats['Q1']['mean_return'] - quantile_stats['Q5']['mean_return']
                strategy_description = "做多Q1(低波动)，做空Q5(高波动)"
            else:  # 高波动率溢价
                long_short_return = quantile_stats['Q5']['mean_return'] - quantile_stats['Q1']['mean_return']
                strategy_description = "做多Q5(高波动)，做空Q1(低波动)"
        else:
            long_short_return = 0
            strategy_description = "无法计算"
        
        results[period] = {
            'ic_mean': ic_mean,
            'ic_std': ic_std,
            'ic_ir': ic_ir,
            'ic_positive_rate': ic_positive_rate,
            'quantile_stats': quantile_stats,
            'long_short_return': long_short_return,
            'strategy_description': strategy_description,
            'daily_count': len(daily_df),
            'avg_stock_count': daily_df['stock_count'].mean()
        }
        
        # 打印结果
        print(f"   📊 IC分析:")
        print(f"      IC均值: {ic_mean:.4f}")
        print(f"      IC标准差: {ic_std:.4f}")
        print(f"      IR: {ic_ir:.4f}")
        print(f"      IC>0比例: {ic_positive_rate:.1%}")
        
        print(f"   📊 分位数收益率:")
        for q in [1, 2, 3, 4, 5]:
            if f'Q{q}' in quantile_stats:
                stats = quantile_stats[f'Q{q}']
                print(f"      Q{q}: {stats['mean_return']:>7.2%} (胜率: {stats['positive_rate']:.1%})")
        
        print(f"   📊 多空策略:")
        print(f"      策略: {strategy_description}")
        print(f"      多空收益: {long_short_return:>7.2%}")
        print(f"   📊 数据统计:")
        print(f"      交易日数: {len(daily_df)}")
        print(f"      平均股票数: {daily_df['stock_count'].mean():.0f}")
    
    return results

def main():
    """主函数"""
    print("🎯 12月波动率因子5分组手动回测分析")
    print("="*60)
    
    # 1. 加载数据
    factor_data, price_data, industry_data = load_data()
    if factor_data is None:
        return
    
    # 2. 计算前向收益率
    forward_returns = calculate_forward_returns(price_data)
    
    # 3. 计算分位数表现
    results = calculate_quantile_performance(factor_data, forward_returns)
    
    # 4. 总结分析
    print("\n" + "="*80)
    print("🎉 手动分组回测分析完成!")
    
    print("\n💡 总结:")
    print("   • 手动计算避免了alphalens可能的计算问题")
    print("   • 基于IC方向自动选择最优多空策略")
    print("   • 提供了详细的统计指标和胜率分析")
    
    # 找出最佳持有期
    best_period = None
    best_ir = 0
    
    for period, result in results.items():
        if abs(result['ic_ir']) > abs(best_ir):
            best_ir = result['ic_ir']
            best_period = period
    
    if best_period:
        print(f"\n🏆 最佳持有期: {best_period}天")
        print(f"   IR: {best_ir:.4f}")
        print(f"   多空收益: {results[best_period]['long_short_return']:.2%}")
        print(f"   策略: {results[best_period]['strategy_description']}")

if __name__ == "__main__":
    main()
