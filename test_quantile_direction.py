#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试alphalens分位数方向是否正确
"""

import pandas as pd
import numpy as np
import sqlite3
from datetime import datetime, timed<PERSON><PERSON>

def test_quantile_direction():
    """测试分位数方向"""
    print("🔍 测试alphalens分位数方向")
    print("="*50)
    
    # 1. 获取一个样本日期的数据
    conn = sqlite3.connect('ganggutong_factor_data.db')
    
    query = """
    SELECT stock_code, volatility_12m
    FROM volatility_12m_factor 
    WHERE date = '2024-01-02' AND volatility_12m IS NOT NULL
    ORDER BY volatility_12m
    LIMIT 20
    """
    
    sample_data = pd.read_sql_query(query, conn)
    conn.close()
    
    print("📊 最低波动率的20只股票:")
    print(sample_data.head(10))
    print(f"波动率范围: {sample_data['volatility_12m'].min():.3f} - {sample_data['volatility_12m'].max():.3f}")
    
    # 2. 使用pandas qcut测试
    print("\n📊 使用pandas qcut分组测试:")
    sample_data['quantile'] = pd.qcut(sample_data['volatility_12m'], q=5, labels=[1,2,3,4,5])
    
    for q in [1,2,3,4,5]:
        subset = sample_data[sample_data['quantile'] == q]
        print(f"Q{q}: 波动率 {subset['volatility_12m'].min():.3f}-{subset['volatility_12m'].max():.3f}")
    
    # 3. 检查alphalens的分位数计算
    print("\n🔍 检查alphalens分位数计算逻辑...")
    
    try:
        import alphalens as al
        
        # 创建简单的测试数据
        dates = pd.date_range('2024-01-01', periods=5, freq='D')
        stocks = ['A', 'B', 'C', 'D', 'E']
        
        # 创建因子数据 (波动率从低到高)
        factor_data = []
        for date in dates:
            for i, stock in enumerate(stocks):
                factor_data.append({
                    'date': date,
                    'stock': stock,
                    'factor': 0.1 + i * 0.1  # 0.1, 0.2, 0.3, 0.4, 0.5
                })
        
        factor_df = pd.DataFrame(factor_data)
        factor_df = factor_df.set_index(['date', 'stock'])['factor']
        
        # 创建价格数据 (假设低波动率股票收益更高)
        price_data = []
        for date in dates:
            for i, stock in enumerate(stocks):
                # 低波动率股票(A)收益高，高波动率股票(E)收益低
                price = 100 * (1 + (5-i) * 0.01)  # A=105, B=104, C=103, D=102, E=101
                price_data.append({
                    'date': date,
                    'stock': stock,
                    'price': price
                })
        
        price_df = pd.DataFrame(price_data)
        price_df = price_df.set_index(['date', 'stock'])['price'].unstack()
        
        print("测试因子数据 (波动率):")
        print(factor_df.unstack().iloc[0])
        print("\n测试价格数据:")
        print(price_df.iloc[0])
        
        # 使用alphalens分析
        factor_data_clean = al.utils.get_clean_factor_and_forward_returns(
            factor=factor_df,
            prices=price_df,
            periods=(1,),
            quantiles=5,
            max_loss=0.5
        )
        
        print("\n📈 Alphalens分位数结果:")
        print(factor_data_clean.groupby('factor_quantile')['factor'].agg(['min', 'max', 'mean']))
        
    except Exception as e:
        print(f"❌ Alphalens测试失败: {e}")

if __name__ == "__main__":
    test_quantile_direction()
