#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用alphalens测试volatility_12m因子的有效性
"""

import sqlite3
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import alphalens as al
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def load_factor_data():
    """加载12月波动率因子数据"""
    print("正在加载12月波动率因子数据...")
    
    conn = sqlite3.connect('ganggutong_factor_data.db')
    
    # 检查数据表是否存在
    tables = pd.read_sql_query("SELECT name FROM sqlite_master WHERE type='table'", conn)
    print(f"数据库中的表: {tables['name'].tolist()}")
    
    # 根据实际表名加载数据
    if 'volatility_12m_factor' in tables['name'].values:
        table_name = 'volatility_12m_factor'
    elif 'volatility_factor' in tables['name'].values:
        table_name = 'volatility_factor'
    else:
        raise ValueError("未找到波动率因子表")
    
    query = f"""
    SELECT stock_code, date, volatility_12m
    FROM {table_name}
    WHERE volatility_12m IS NOT NULL
    AND date >= '2020-01-01'  -- 使用最近5年数据
    ORDER BY date, stock_code
    """
    
    factor_data = pd.read_sql_query(query, conn)
    conn.close()
    
    print(f"加载了 {len(factor_data)} 条因子数据")
    print(f"日期范围: {factor_data['date'].min()} 到 {factor_data['date'].max()}")
    print(f"股票数量: {factor_data['stock_code'].nunique()}")
    
    return factor_data

def load_price_data():
    """加载价格数据"""
    print("正在加载价格数据...")
    
    conn = sqlite3.connect('ganggutong_10year_data.db')
    
    query = """
    SELECT stock_code, date, close
    FROM stock_prices
    WHERE date >= '2020-01-01'
    AND close IS NOT NULL
    ORDER BY date, stock_code
    """

    price_data = pd.read_sql_query(query, conn)
    conn.close()

    # 使用收盘价
    price_data['price'] = price_data['close']
    
    print(f"加载了 {len(price_data)} 条价格数据")
    
    return price_data[['stock_code', 'date', 'price']]

def prepare_alphalens_data(factor_data, price_data):
    """准备alphalens所需的数据格式"""
    print("正在准备alphalens数据格式...")
    
    # 转换日期格式
    factor_data['date'] = pd.to_datetime(factor_data['date'])
    price_data['date'] = pd.to_datetime(price_data['date'])
    
    # 创建因子数据的MultiIndex格式
    factor_df = factor_data.set_index(['date', 'stock_code'])['volatility_12m']
    factor_df = factor_df.unstack('stock_code')
    
    # 创建价格数据的MultiIndex格式
    price_df = price_data.set_index(['date', 'stock_code'])['price']
    price_df = price_df.unstack('stock_code')
    
    # 确保日期对齐
    common_dates = factor_df.index.intersection(price_df.index)
    common_stocks = factor_df.columns.intersection(price_df.columns)
    
    factor_df = factor_df.loc[common_dates, common_stocks]
    price_df = price_df.loc[common_dates, common_stocks]
    
    print(f"对齐后数据维度: {factor_df.shape}")
    print(f"日期范围: {factor_df.index.min()} 到 {factor_df.index.max()}")
    print(f"股票数量: {len(common_stocks)}")
    
    # 转换为alphalens需要的格式
    factor_series = factor_df.stack()
    factor_series.index.names = ['date', 'asset']
    
    price_panel = price_df
    
    return factor_series, price_panel

def run_alphalens_analysis(factor_data, price_data):
    """运行alphalens分析"""
    print("开始alphalens因子分析...")
    
    try:
        # 创建alphalens数据
        factor_data_clean = al.utils.get_clean_factor_and_forward_returns(
            factor=factor_data,
            prices=price_data,
            quantiles=5,  # 分5个分位数
            periods=(1, 5, 10, 21),  # 1天、5天、10天、21天的前瞻收益
            max_loss=0.35,  # 允许35%的数据缺失
        )
        
        print("alphalens数据准备完成")
        print(f"因子数据形状: {factor_data_clean.shape}")
        
        return factor_data_clean
        
    except Exception as e:
        print(f"alphalens数据准备失败: {e}")
        return None

def create_factor_analysis_report(factor_data_clean):
    """创建因子分析报告"""
    if factor_data_clean is None:
        print("无法创建报告，因子数据为空")
        return
    
    print("正在生成因子分析报告...")
    
    # 设置图形样式
    plt.style.use('default')
    
    # 1. 因子收益分析
    print("1. 分析因子收益...")
    mean_return_by_q, std_return_by_q = al.performance.mean_return_by_quantile(factor_data_clean)
    
    # 2. 信息系数分析
    print("2. 分析信息系数...")
    ic = al.performance.factor_information_coefficient(factor_data_clean)
    
    # 3. 换手率分析
    print("3. 分析换手率...")
    turnover = al.performance.factor_rank_autocorrelation(factor_data_clean)
    
    # 创建综合报告
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    fig.suptitle('12月波动率因子分析报告', fontsize=16, fontweight='bold')
    
    # 分位数收益图
    mean_return_by_q.plot(kind='bar', ax=axes[0,0])
    axes[0,0].set_title('各分位数平均收益')
    axes[0,0].set_xlabel('分位数')
    axes[0,0].set_ylabel('平均收益')
    axes[0,0].legend(title='持有期')
    
    # IC时间序列图
    ic.plot(ax=axes[0,1])
    axes[0,1].set_title('信息系数时间序列')
    axes[0,1].set_xlabel('日期')
    axes[0,1].set_ylabel('IC')
    axes[0,1].legend(title='持有期')
    
    # IC分布直方图
    ic['1D'].hist(bins=50, ax=axes[1,0], alpha=0.7)
    axes[1,0].set_title('1天IC分布')
    axes[1,0].set_xlabel('IC值')
    axes[1,0].set_ylabel('频数')
    axes[1,0].axvline(ic['1D'].mean(), color='red', linestyle='--', label=f'均值: {ic["1D"].mean():.3f}')
    axes[1,0].legend()
    
    # 因子自相关性
    turnover.plot(ax=axes[1,1])
    axes[1,1].set_title('因子排名自相关性')
    axes[1,1].set_xlabel('滞后期')
    axes[1,1].set_ylabel('自相关系数')
    
    plt.tight_layout()
    plt.savefig('volatility_12m_factor_analysis.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    # 打印统计摘要
    print("\n=== 12月波动率因子分析摘要 ===")
    print(f"IC均值 (1天): {ic['1D'].mean():.4f}")
    print(f"IC标准差 (1天): {ic['1D'].std():.4f}")
    print(f"IC_IR (1天): {ic['1D'].mean() / ic['1D'].std():.4f}")
    print(f"IC>0的比例 (1天): {(ic['1D'] > 0).mean():.2%}")
    
    print(f"\nIC均值 (5天): {ic['5D'].mean():.4f}")
    print(f"IC标准差 (5天): {ic['5D'].std():.4f}")
    print(f"IC_IR (5天): {ic['5D'].mean() / ic['5D'].std():.4f}")
    
    print(f"\nIC均值 (21天): {ic['21D'].mean():.4f}")
    print(f"IC标准差 (21天): {ic['21D'].std():.4f}")
    print(f"IC_IR (21天): {ic['21D'].mean() / ic['21D'].std():.4f}")
    
    # 分位数收益分析
    print(f"\n=== 分位数收益分析 (1天持有期) ===")
    q1_ret = mean_return_by_q.loc[1, '1D']
    q5_ret = mean_return_by_q.loc[5, '1D']
    print(f"第1分位数(低波动率)平均收益: {q1_ret:.4f}")
    print(f"第5分位数(高波动率)平均收益: {q5_ret:.4f}")
    print(f"多空收益差(Q5-Q1): {q5_ret - q1_ret:.4f}")
    
    return {
        'ic_stats': ic.describe(),
        'mean_return_by_q': mean_return_by_q,
        'turnover': turnover
    }

def main():
    """主函数"""
    try:
        # 1. 加载数据
        factor_data = load_factor_data()
        price_data = load_price_data()
        
        # 2. 准备alphalens数据
        factor_series, price_panel = prepare_alphalens_data(factor_data, price_data)
        
        # 3. 运行alphalens分析
        factor_data_clean = run_alphalens_analysis(factor_series, price_panel)
        
        # 4. 生成分析报告
        if factor_data_clean is not None:
            results = create_factor_analysis_report(factor_data_clean)
            print("\n✅ 12月波动率因子分析完成！")
            print("📊 分析图表已保存为 'volatility_12m_factor_analysis.png'")
        else:
            print("❌ 因子分析失败")
            
    except Exception as e:
        print(f"❌ 分析过程中出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
