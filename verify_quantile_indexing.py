#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证alphalens分位数收益的索引顺序
"""

import sqlite3
import pandas as pd
import numpy as np
import alphalens as al
import warnings
warnings.filterwarnings('ignore')

def load_sample_data():
    """加载样本数据"""
    print("📊 加载样本数据...")
    
    # 加载因子数据
    conn = sqlite3.connect('ganggutong_factor_data.db')
    factor_query = '''
    SELECT stock_code, date, volatility_12m
    FROM volatility_12m_factor 
    WHERE date >= '2024-01-01' AND date <= '2024-01-31'
    ORDER BY date, stock_code
    '''
    factor_data = pd.read_sql_query(factor_query, conn)
    conn.close()
    
    # 加载价格数据
    conn = sqlite3.connect('ganggutong_10year_data.db')
    price_query = '''
    SELECT stock_code, date, close
    FROM stock_prices 
    WHERE date >= '2024-01-01' AND date <= '2024-02-29'
    ORDER BY date, stock_code
    '''
    price_data = pd.read_sql_query(price_query, conn)
    conn.close()
    
    return factor_data, price_data

def prepare_alphalens_data(factor_data, price_data):
    """准备alphalens数据"""
    print("🔧 准备alphalens数据...")
    
    # 转换日期格式
    factor_data['date'] = pd.to_datetime(factor_data['date'])
    price_data['date'] = pd.to_datetime(price_data['date'])
    
    # 创建价格透视表
    prices = price_data.pivot(index='date', columns='stock_code', values='close')
    prices = prices.sort_index()
    
    # 创建因子透视表
    factor_pivot = factor_data.pivot(index='date', columns='stock_code', values='volatility_12m')
    factor_pivot = factor_pivot.sort_index()
    
    # 转换为alphalens格式
    factor_series = factor_pivot.stack()
    factor_series.index.names = ['date', 'asset']
    factor_series = factor_series.dropna()
    
    return factor_series, prices

def verify_quantile_indexing():
    """验证分位数索引顺序"""
    print("🎯 验证alphalens分位数索引顺序")
    print("="*50)
    
    # 1. 加载数据
    factor_data, price_data = load_sample_data()
    factor_series, prices = prepare_alphalens_data(factor_data, price_data)
    
    # 2. 运行alphalens
    print("\n🚀 运行alphalens分析...")
    factor_data_clean = al.utils.get_clean_factor_and_forward_returns(
        factor=factor_series,
        prices=prices,
        periods=(1, 5, 20),
        quantiles=5,
        max_loss=0.35
    )
    
    # 3. 计算分位数收益
    mean_return_by_q, _ = al.performance.mean_return_by_quantile(factor_data_clean)
    
    print(f"\n📊 分位数收益结果:")
    print(f"数据形状: {mean_return_by_q.shape}")
    print(f"索引: {mean_return_by_q.index}")
    print(f"列名: {list(mean_return_by_q.columns)}")
    print(f"\n完整数据:")
    print(mean_return_by_q)
    
    # 4. 验证索引含义
    print(f"\n🔍 验证索引含义:")
    
    # 检查第一个日期的因子分位数分布
    sample_date = factor_data_clean.index.get_level_values('date')[0]
    sample_data = factor_data_clean.loc[sample_date]
    
    print(f"样本日期: {sample_date}")
    print(f"样本数据量: {len(sample_data)}")
    
    # 按分位数分组查看因子值分布
    for q in [1, 2, 3, 4, 5]:
        q_data = sample_data[sample_data['factor_quantile'] == q]
        if len(q_data) > 0:
            factor_min = q_data['factor'].min()
            factor_max = q_data['factor'].max()
            factor_mean = q_data['factor'].mean()
            print(f"分位数 {q}: 因子值范围 [{factor_min:.4f}, {factor_max:.4f}], 平均={factor_mean:.4f}, 数量={len(q_data)}")
    
    # 5. 验证收益率与分位数的对应关系
    print(f"\n📈 验证收益率与分位数对应关系:")
    
    for period in ['1D', '5D', '20D']:
        if period in mean_return_by_q.columns:
            print(f"\n{period} 期间:")
            period_returns = mean_return_by_q[period]
            
            print("分位数索引顺序验证:")
            for i, (quantile, return_val) in enumerate(period_returns.items()):
                print(f"  索引位置 {i}: 分位数 {quantile}, 收益率 {return_val:.4f}")
            
            # 验证逻辑
            q1_return = period_returns.iloc[0]  # 第一个位置
            q5_return = period_returns.iloc[-1]  # 最后一个位置
            
            q1_index = period_returns.index[0]  # 第一个位置的索引值
            q5_index = period_returns.index[-1]  # 最后一个位置的索引值
            
            print(f"  ⚠️  关键验证:")
            print(f"     iloc[0] (第一个位置): 分位数={q1_index}, 收益率={q1_return:.4f}")
            print(f"     iloc[-1] (最后一个位置): 分位数={q5_index}, 收益率={q5_return:.4f}")
            
            # 检查是否Q1确实是低波动率，Q5确实是高波动率
            if q1_index == 1 and q5_index == 5:
                print(f"     ✅ 索引顺序正确: Q1在第一位，Q5在最后一位")
            else:
                print(f"     ❌ 索引顺序异常: 第一位是Q{q1_index}，最后一位是Q{q5_index}")
    
    # 6. 计算IC并验证一致性
    print(f"\n🔍 IC一致性验证:")
    ic = al.performance.factor_information_coefficient(factor_data_clean)
    
    for period in ['1D', '5D', '20D']:
        if period in ic.columns and period in mean_return_by_q.columns:
            ic_mean = ic[period].mean()
            period_returns = mean_return_by_q[period]
            
            q1_return = period_returns.loc[1]  # 明确使用分位数1
            q5_return = period_returns.loc[5]  # 明确使用分位数5
            
            print(f"\n{period}:")
            print(f"  IC均值: {ic_mean:.4f}")
            print(f"  Q1(低波动)收益: {q1_return:.4f}")
            print(f"  Q5(高波动)收益: {q5_return:.4f}")
            
            # 检查逻辑一致性
            if ic_mean < 0:
                expected = "Q1 > Q5"
                actual = "Q1 > Q5" if q1_return > q5_return else "Q1 < Q5"
                consistent = q1_return > q5_return
            else:
                expected = "Q5 > Q1"
                actual = "Q5 > Q1" if q5_return > q1_return else "Q5 < Q1"
                consistent = q5_return > q1_return
            
            print(f"  预期关系: {expected}")
            print(f"  实际关系: {actual}")
            print(f"  逻辑一致: {'✅' if consistent else '❌'}")
            
            # 如果使用iloc会怎样？
            q1_return_iloc = period_returns.iloc[0]
            q5_return_iloc = period_returns.iloc[-1]
            
            print(f"  如果使用iloc[0]: {q1_return_iloc:.4f}")
            print(f"  如果使用iloc[-1]: {q5_return_iloc:.4f}")
            
            if q1_return_iloc != q1_return or q5_return_iloc != q5_return:
                print(f"  ⚠️  iloc和loc结果不同！这可能是原始脚本错误的原因")

def main():
    """主函数"""
    verify_quantile_indexing()

if __name__ == "__main__":
    main()
