#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
12月波动率因子行业中性化分析
对比原始因子和行业中性化因子的有效性
"""

import sqlite3
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

try:
    import alphalens as al
    ALPHALENS_AVAILABLE = True
    print(f"✅ 使用alphalens进行专业因子分析 (版本: {al.__version__})")
except ImportError:
    ALPHALENS_AVAILABLE = False
    print("❌ alphalens未安装，使用简化分析")

def load_data():
    """加载数据"""
    print("\n📊 加载数据...")
    
    # 1. 加载行业数据
    try:
        industry_df = pd.read_csv('data_files/ganggutong_consti.csv')
        industry_df['代码'] = industry_df['代码'].astype(str).str.zfill(5)
        print(f"✅ 行业数据: {len(industry_df)} 只股票, {industry_df['所属行业'].nunique()} 个行业")
        
        # 显示主要行业分布
        top_industries = industry_df['所属行业'].value_counts().head(10)
        print("主要行业分布:")
        for industry, count in top_industries.items():
            print(f"  {industry}: {count}只")
            
    except Exception as e:
        print(f"❌ 无法加载行业数据: {str(e)}")
        return None, None, None
    
    # 2. 加载12月波动率因子数据（从2015年开始，获得更长期分析）
    conn_factor = sqlite3.connect('ganggutong_factor_data.db')

    # 检查表名
    tables = pd.read_sql_query("SELECT name FROM sqlite_master WHERE type='table'", conn_factor)
    if 'volatility_12m_factor' in tables['name'].values:
        table_name = 'volatility_12m_factor'
    elif 'volatility_factor' in tables['name'].values:
        table_name = 'volatility_factor'
    else:
        print("❌ 未找到波动率因子表")
        conn_factor.close()
        return None, None, None

    factor_query = f"""
        SELECT stock_code, date, volatility_12m
        FROM {table_name}
        WHERE date >= '2015-01-01'
        AND volatility_12m IS NOT NULL
        ORDER BY date, stock_code
    """
    factor_data = pd.read_sql_query(factor_query, conn_factor)
    conn_factor.close()
    
    # 3. 加载价格数据
    stock_codes = factor_data['stock_code'].unique()
    stock_codes_str = "','".join(stock_codes)

    conn_price = sqlite3.connect('ganggutong_10year_data.db')
    price_query = f"""
        SELECT stock_code, date, close
        FROM stock_prices
        WHERE stock_code IN ('{stock_codes_str}')
        AND date >= '2015-01-01'
        ORDER BY date, stock_code
    """
    price_data = pd.read_sql_query(price_query, conn_price)
    conn_price.close()
    
    print(f"✅ 因子数据: {len(factor_data):,} 条")
    print(f"✅ 价格数据: {len(price_data):,} 条")
    
    return factor_data, price_data, industry_df

def industry_neutralize_factor(factor_data, industry_data, factor_name):
    """对因子进行行业中性化处理"""
    print(f"\n🔧 对 {factor_name} 进行行业中性化...")
    
    # 转换日期格式
    factor_data['date'] = pd.to_datetime(factor_data['date'])
    
    # 合并行业信息
    factor_with_industry = factor_data.merge(
        industry_data[['代码', '所属行业']], 
        left_on='stock_code', 
        right_on='代码', 
        how='left'
    )
    
    print(f"   合并后数据: {len(factor_with_industry)} 条")
    print(f"   有行业信息的数据: {factor_with_industry['所属行业'].notna().sum()} 条")
    
    # 按日期进行行业中性化
    neutralized_data = []
    
    for date in factor_with_industry['date'].unique():
        date_data = factor_with_industry[factor_with_industry['date'] == date].copy()
        
        # 按行业分组进行标准化
        for industry in date_data['所属行业'].dropna().unique():
            industry_mask = date_data['所属行业'] == industry
            industry_subset = date_data[industry_mask]
            
            if len(industry_subset) > 1:
                # 行业内标准化：(x - industry_mean) / industry_std
                industry_values = industry_subset[factor_name]
                if industry_values.std() > 1e-8:  # 避免除零
                    industry_mean = industry_values.mean()
                    industry_std = industry_values.std()
                    date_data.loc[industry_mask, f'{factor_name}_neutral'] = (
                        (industry_values - industry_mean) / industry_std
                    )
                else:
                    date_data.loc[industry_mask, f'{factor_name}_neutral'] = 0
            else:
                # 如果行业内只有一只股票，设为0
                date_data.loc[industry_mask, f'{factor_name}_neutral'] = 0
        
        # 对没有行业信息的股票，设为原始因子值的标准化
        no_industry_mask = date_data['所属行业'].isna()
        if no_industry_mask.sum() > 0:
            no_industry_values = date_data.loc[no_industry_mask, factor_name]
            if len(no_industry_values) > 1 and no_industry_values.std() > 1e-8:
                mean_val = no_industry_values.mean()
                std_val = no_industry_values.std()
                date_data.loc[no_industry_mask, f'{factor_name}_neutral'] = (
                    (no_industry_values - mean_val) / std_val
                )
            else:
                date_data.loc[no_industry_mask, f'{factor_name}_neutral'] = 0
        
        neutralized_data.append(date_data)
    
    result = pd.concat(neutralized_data, ignore_index=True)
    
    # 检查中性化效果
    original_factor = result[factor_name].dropna()
    neutralized_factor = result[f'{factor_name}_neutral'].dropna()
    
    print(f"   📊 原始因子统计: 均值={original_factor.mean():.4f}, 标准差={original_factor.std():.4f}")
    print(f"   📊 中性化因子统计: 均值={neutralized_factor.mean():.4f}, 标准差={neutralized_factor.std():.4f}")
    
    return result

def prepare_alphalens_data(factor_data, price_data, factor_name):
    """准备alphalens数据"""
    print(f"\n🔧 准备 {factor_name} 的alphalens数据...")
    
    # 转换日期格式
    factor_data['date'] = pd.to_datetime(factor_data['date'])
    price_data['date'] = pd.to_datetime(price_data['date'])
    
    # 创建价格透视表
    prices = price_data.pivot(index='date', columns='stock_code', values='close')
    prices = prices.sort_index()
    
    # 创建因子数据
    factor_pivot = factor_data.pivot(index='date', columns='stock_code', values=factor_name)
    factor_pivot = factor_pivot.sort_index()
    
    # 转换为alphalens格式
    factor_series = factor_pivot.stack()
    factor_series.index.names = ['date', 'asset']
    factor_series = factor_series.dropna()
    
    print(f"   价格数据形状: {prices.shape}")
    print(f"   因子数据点: {len(factor_series):,}")
    
    return factor_series, prices

def run_alphalens_analysis(factor_data, prices, factor_name):
    """运行alphalens分析"""
    print(f"\n🚀 运行 {factor_name} 的alphalens分析...")
    
    try:
        # 创建alphalens数据集
        print("   创建因子数据集...")
        factor_data_clean = al.utils.get_clean_factor_and_forward_returns(
            factor=factor_data,
            prices=prices,
            periods=(1, 5, 10, 20, 30, 60, 90, 120, 180, 240),  # 前瞻收益期
            quantiles=5,  # 分位数
            max_loss=0.35  # 允许一定数据缺失
        )
        
        print("   计算IC...")
        # 计算IC分析
        ic = al.performance.factor_information_coefficient(factor_data_clean)
        
        print("   计算分位数收益...")
        # 计算分位数收益率
        mean_return_by_q, _ = al.performance.mean_return_by_quantile(factor_data_clean)
        
        return {
            'factor_data': factor_data_clean,
            'ic': ic,
            'mean_return_by_q': mean_return_by_q
        }
        
    except Exception as e:
        print(f"   ❌ 分析失败: {str(e)}")
        return None

def analyze_factor_effectiveness(results, factor_name):
    """分析因子有效性"""
    if not results:
        return {}
    
    print(f"\n📈 {factor_name} 因子有效性分析")
    print("="*60)
    
    # IC分析
    ic = results['ic']
    print("📊 IC分析:")
    print("期间     IC均值    IC标准差   IR      IC>0比例")
    print("-" * 50)
    
    ic_stats = {}
    for period in ic.columns:
        ic_mean = ic[period].mean()
        ic_std = ic[period].std()
        ir = ic_mean / ic_std if ic_std > 0 else 0
        ic_positive_rate = (ic[period] > 0).mean()
        
        print(f"{period:>6}   {ic_mean:>7.4f}   {ic_std:>7.4f}   {ir:>6.3f}   {ic_positive_rate:>7.1%}")
        
        ic_stats[period] = {
            'ic_mean': ic_mean,
            'ic_std': ic_std,
            'ir': ir,
            'ic_positive_rate': ic_positive_rate
        }
    
    # 验证分位数定义
    print(f"\n🔍 验证alphalens分位数定义:")
    print("="*60)

    # 获取因子数据用于验证
    factor_values = results['factor_data'].reset_index()

    # 检查几个日期的分位数定义
    sample_dates = factor_values['date'].unique()[:3]  # 检查前3个日期

    for i, date in enumerate(sample_dates):
        date_data = factor_values[factor_values['date'] == date].copy()
        if len(date_data) < 50:
            continue

        print(f"\n📅 日期 {i+1}: {date}")

        # 手工计算分位数
        # 获取实际的因子列名
        print(f"   可用列名: {list(date_data.columns)}")

        # 查找因子列
        factor_cols = [col for col in date_data.columns if 'volatility' in col or col == 'factor']
        if len(factor_cols) == 0:
            print("   ⚠️  未找到因子列，跳过验证")
            break

        factor_col = factor_cols[0]
        print(f"   使用因子列: {factor_col}")
        date_data_sorted = date_data.sort_values(factor_col)
        n = len(date_data_sorted)

        # 计算各分位数的波动率范围
        q1_data = date_data_sorted.iloc[:n//5]  # 最低20%
        q5_data = date_data_sorted.iloc[4*n//5:]  # 最高20%

        print(f"   Q1(最低20%): 波动率范围 [{q1_data[factor_col].min():.4f}, {q1_data[factor_col].max():.4f}], 平均={q1_data[factor_col].mean():.4f}")
        print(f"   Q5(最高20%): 波动率范围 [{q5_data[factor_col].min():.4f}, {q5_data[factor_col].max():.4f}], 平均={q5_data[factor_col].mean():.4f}")

        # 验证alphalens的分位数标签
        try:
            date_data['quantile'] = pd.qcut(date_data[factor_col], q=5, labels=[1,2,3,4,5], duplicates='drop')

            print(f"   alphalens分位数验证:")
            for q in [1, 5]:  # 只检查Q1和Q5
                q_data = date_data[date_data['quantile'] == q]
                if len(q_data) > 0:
                    print(f"     alphalens Q{q}: 波动率范围 [{q_data[factor_col].min():.4f}, {q_data[factor_col].max():.4f}], 平均={q_data[factor_col].mean():.4f}")
        except:
            print(f"   ⚠️  无法计算分位数（可能存在重复值）")

        if i == 0:  # 只详细检查第一个日期
            break

    print(f"\n� 结论:")
    print(f"   • Q1 = 波动率最低的20%股票（低波动率组合）")
    print(f"   • Q5 = 波动率最高的20%股票（高波动率组合）")

    # 分位数分析
    print(f"\n📊 分位数收益率分析:")
    mean_return_by_q = results['mean_return_by_q']
    
    quintile_stats = {}
    for period in mean_return_by_q.columns:
        print(f"\n{period} 收益率:")
        period_returns = mean_return_by_q[period]
        for quintile in period_returns.index:
            print(f"  {quintile}: {period_returns[quintile]:>7.2%}")
        
        # 多空收益 (基于IC方向确定策略)
        if len(period_returns) >= 5:
            # 如果IC<0，说明低波动率表现更好，多空策略：做多Q1，做空Q5
            # 如果IC>0，说明高波动率表现更好，多空策略：做多Q5，做空Q1
            long_short_high_low = period_returns.iloc[-1] - period_returns.iloc[0]  # Q5 - Q1
            long_short_low_high = period_returns.iloc[0] - period_returns.iloc[-1]  # Q1 - Q5
            print(f"  多空收益(高波动-低波动): {long_short_high_low:>7.2%}")
            print(f"  多空收益(低波动-高波动): {long_short_low_high:>7.2%}")
            quintile_stats[period] = {
                'long_short_return': long_short_low_high,  # 使用低波动-高波动的收益
                'q1_return': period_returns.iloc[0],
                'q5_return': period_returns.iloc[-1]
            }
    
    return {
        'ic_stats': ic_stats,
        'quintile_stats': quintile_stats
    }

def compare_factors(original_stats, neutral_stats, factor_name):
    """对比原始因子和中性化因子"""
    print(f"\n🔍 {factor_name} 原始 vs 中性化对比")
    print("="*70)

    # 获取所有持有期
    periods = list(original_stats.get('ic_stats', {}).keys())

    print("📊 所有持有期IC对比:")
    print("持有期     原始IR    中性化IR    原始多空收益  中性化多空收益")
    print("-" * 65)

    best_period_orig = None
    best_period_neut = None
    best_ir_orig = 0
    best_ir_neut = 0
    best_ret_orig = 0
    best_ret_neut = 0

    for period in periods:
        if period in original_stats.get('ic_stats', {}) and period in neutral_stats.get('ic_stats', {}):
            orig_ic = original_stats['ic_stats'][period]
            neut_ic = neutral_stats['ic_stats'][period]

            orig_ir = abs(orig_ic['ir'])
            neut_ir = abs(neut_ic['ir'])

            orig_ret = original_stats.get('quintile_stats', {}).get(period, {}).get('long_short_return', 0)
            neut_ret = neutral_stats.get('quintile_stats', {}).get(period, {}).get('long_short_return', 0)

            print(f"{period:>6}     {orig_ir:>7.3f}     {neut_ir:>7.3f}       {orig_ret:>7.2%}       {neut_ret:>7.2%}")

            # 记录最佳表现
            if orig_ir > best_ir_orig:
                best_ir_orig = orig_ir
                best_period_orig = period
                best_ret_orig = orig_ret

            if neut_ir > best_ir_neut:
                best_ir_neut = neut_ir
                best_period_neut = period
                best_ret_neut = neut_ret

    print(f"\n🏆 最佳持有期:")
    print(f"原始因子最佳: {best_period_orig} (IR={best_ir_orig:.3f}, 多空收益={best_ret_orig:.2%})")
    print(f"中性化因子最佳: {best_period_neut} (IR={best_ir_neut:.3f}, 多空收益={best_ret_neut:.2%})")

    # 总体评价
    if best_ir_neut > best_ir_orig:
        print("✅ 行业中性化提升了因子效果")
    else:
        print("⚠️  行业中性化效果有限")

def main():
    """主函数"""
    print("🎯 12月波动率因子行业中性化分析")
    print("="*60)
    
    # 1. 加载数据
    factor_data, price_data, industry_data = load_data()
    
    if factor_data is None or price_data is None or industry_data is None:
        print("❌ 数据加载失败，退出分析")
        return
    
    if not ALPHALENS_AVAILABLE:
        print("❌ 需要安装alphalens库进行专业分析")
        return
    
    # 2. 分析12月波动率因子
    factor_name = 'volatility_12m'
    
    print(f"\n{'='*80}")
    print(f"分析因子: {factor_name}")
    print(f"{'='*80}")
    
    # 2.1 原始因子分析
    print(f"\n🔸 原始因子分析")
    factor_series_orig, prices = prepare_alphalens_data(factor_data, price_data, factor_name)
    results_orig = run_alphalens_analysis(factor_series_orig, prices, f"{factor_name}_原始")
    stats_orig = analyze_factor_effectiveness(results_orig, f"{factor_name}_原始")
    
    # 2.2 行业中性化
    neutralized_data = industry_neutralize_factor(factor_data, industry_data, factor_name)
    
    # 2.3 中性化因子分析
    print(f"\n🔸 中性化因子分析")
    factor_series_neutral, _ = prepare_alphalens_data(neutralized_data, price_data, f"{factor_name}_neutral")
    results_neutral = run_alphalens_analysis(factor_series_neutral, prices, f"{factor_name}_中性化")
    stats_neutral = analyze_factor_effectiveness(results_neutral, f"{factor_name}_中性化")
    
    # 2.4 对比分析
    compare_factors(stats_orig, stats_neutral, factor_name)
    
    print("\n" + "-"*80)
    
    print(f"\n🎉 分析完成!")
    print(f"\n💡 总结:")
    print(f"   • 波动率因子反映股票价格波动特征")
    print(f"   • 行业中性化消除了行业间波动率差异的影响")
    print(f"   • 中性化后的因子更能反映个股相对波动率水平")
    print(f"   • 建议根据投资策略选择合适的因子版本")
    print(f"   • 个股选择策略：使用中性化因子")
    print(f"   • 行业轮动策略：使用原始因子")

if __name__ == "__main__":
    main()
