#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试alphalens前向收益计算问题
"""

import sqlite3
import pandas as pd
import numpy as np
import alphalens as al
from scipy.stats import pearsonr
import warnings
warnings.filterwarnings('ignore')

def load_sample_data():
    """加载样本数据"""
    print("📊 加载样本数据...")
    
    # 加载因子数据 (2015年6月30号到2025年6月30号)
    conn = sqlite3.connect('ganggutong_factor_data.db')
    factor_query = '''
    SELECT stock_code, date, volatility_12m
    FROM volatility_12m_factor
    WHERE date >= '2015-06-30' AND date <= '2025-06-30'
    ORDER BY date, stock_code
    '''
    factor_data = pd.read_sql_query(factor_query, conn)
    conn.close()

    # 加载价格数据 (2015年6月30号到2025年6月30号)
    conn = sqlite3.connect('ganggutong_10year_data.db')
    price_query = '''
    SELECT stock_code, date, close
    FROM stock_prices
    WHERE date >= '2015-06-30' AND date <= '2025-06-30'
    ORDER BY date, stock_code
    '''
    price_data = pd.read_sql_query(price_query, conn)
    conn.close()
    
    print(f"✅ 因子数据: {len(factor_data)} 条")
    print(f"✅ 价格数据: {len(price_data)} 条")
    
    return factor_data, price_data

def prepare_alphalens_data(factor_data, price_data):
    """准备alphalens格式数据"""
    print("\n🔧 准备alphalens数据...")
    
    # 转换日期格式
    factor_data['date'] = pd.to_datetime(factor_data['date'])
    price_data['date'] = pd.to_datetime(price_data['date'])
    
    # 创建因子Series (MultiIndex: date, stock_code)
    factor_series = factor_data.set_index(['date', 'stock_code'])['volatility_12m']
    factor_series = factor_series.dropna()
    
    # 创建价格DataFrame (index: date, columns: stock_code)
    prices = price_data.pivot(index='date', columns='stock_code', values='close')
    
    print(f"   因子数据点: {len(factor_series):,}")
    print(f"   价格数据形状: {prices.shape}")
    
    return factor_series, prices

def manual_forward_returns(prices, periods=[1]):
    """手工计算前向收益率"""
    print("\n🔧 手工计算前向收益率...")
    
    forward_returns = {}
    
    for period in periods:
        print(f"   计算 {period} 天前向收益...")
        
        # 手工计算：未来period天的收益率
        forward_ret = prices.pct_change(periods=period).shift(-period)
        forward_returns[period] = forward_ret
        
        # 统计有效数据
        valid_count = forward_ret.count().sum()
        print(f"   ✅ {period}天前向收益: {valid_count} 个有效数据点")
    
    return forward_returns

def compare_alphalens_vs_manual(factor_series, prices):
    """对比alphalens和手工计算的结果"""
    print("\n🔍 对比alphalens vs 手工计算...")
    print("="*60)
    
    # 1. alphalens计算
    print("1️⃣ alphalens计算:")
    try:
        factor_data_clean = al.utils.get_clean_factor_and_forward_returns(
            factor=factor_series,
            prices=prices,
            periods=(1,),  # 只计算1天
            quantiles=5,
            max_loss=0.35
        )
        
        print(f"   ✅ alphalens数据: {len(factor_data_clean)} 条")
        
        # 计算IC
        ic_alphalens = al.performance.factor_information_coefficient(factor_data_clean)
        print(f"   📊 IC数据形状: {ic_alphalens.shape}")
        print(f"   📊 IC列名: {list(ic_alphalens.columns)}")

        ic_1d_alphalens = ic_alphalens.iloc[:, 0].mean()  # 使用第一列

        # 计算分位数收益
        mean_return_by_q, _ = al.performance.mean_return_by_quantile(factor_data_clean)
        print(f"   📊 分位数收益形状: {mean_return_by_q.shape}")
        print(f"   📊 分位数收益索引: {mean_return_by_q.index}")
        print(f"   📊 分位数收益列名: {list(mean_return_by_q.columns)}")

        q1_return_alphalens = mean_return_by_q.loc[1, '1D']  # Q1分位数
        q5_return_alphalens = mean_return_by_q.loc[5, '1D']  # Q5分位数
        
        print(f"   📊 alphalens IC (1D): {ic_1d_alphalens:.4f}")
        print(f"   📊 alphalens Q1收益: {q1_return_alphalens:.4f}")
        print(f"   📊 alphalens Q5收益: {q5_return_alphalens:.4f}")
        
    except Exception as e:
        print(f"   ❌ alphalens计算失败: {e}")
        return
    
    # 2. 手工计算
    print("\n2️⃣ 手工计算:")
    
    # 手工计算前向收益
    forward_returns = manual_forward_returns(prices, periods=[1])
    
    # 合并因子和收益数据进行对比
    factor_df = factor_series.reset_index()
    factor_df.columns = ['date', 'stock_code', 'volatility_12m']  # 重命名列

    results = []
    for date, group in factor_df.groupby('date'):
        if len(group) < 50:
            continue

        # 获取当日前向收益
        if date in forward_returns[1].index:
            forward_ret_day = forward_returns[1].loc[date]

            # 找到共同股票
            common_stocks = set(group['stock_code']).intersection(set(forward_ret_day.dropna().index))
            
            if len(common_stocks) >= 50:
                # 构建当日数据
                day_data = []
                for stock in common_stocks:
                    factor_val = group[group['stock_code'] == stock]['volatility_12m'].iloc[0]
                    return_val = forward_ret_day[stock]
                    if not pd.isna(factor_val) and not pd.isna(return_val):
                        day_data.append({'factor': factor_val, 'return': return_val})
                
                if len(day_data) >= 50:
                    day_df = pd.DataFrame(day_data)
                    
                    # 计算IC
                    ic_manual = day_df['factor'].corr(day_df['return'])
                    
                    # 计算分位数收益
                    day_df_sorted = day_df.sort_values('factor')
                    n = len(day_df_sorted)
                    
                    q1_return_manual = day_df_sorted.iloc[:n//5]['return'].mean()
                    q5_return_manual = day_df_sorted.iloc[4*n//5:]['return'].mean()
                    
                    results.append({
                        'date': date,
                        'ic': ic_manual,
                        'q1_return': q1_return_manual,
                        'q5_return': q5_return_manual,
                        'stock_count': len(day_df)
                    })
    
    if results:
        results_df = pd.DataFrame(results)
        
        ic_manual_mean = results_df['ic'].mean()
        q1_manual_mean = results_df['q1_return'].mean()
        q5_manual_mean = results_df['q5_return'].mean()
        
        print(f"   ✅ 手工数据: {len(results_df)} 个交易日")
        print(f"   📊 手工IC (1D): {ic_manual_mean:.4f}")
        print(f"   📊 手工Q1收益: {q1_manual_mean:.4f}")
        print(f"   📊 手工Q5收益: {q5_manual_mean:.4f}")
        
        # 3. 对比结果
        print("\n3️⃣ 结果对比:")
        print("="*40)
        print(f"{'指标':<12} {'alphalens':<12} {'手工计算':<12} {'差异':<12}")
        print("-" * 48)
        print(f"{'IC (1D)':<12} {ic_1d_alphalens:<12.4f} {ic_manual_mean:<12.4f} {abs(ic_1d_alphalens - ic_manual_mean):<12.4f}")
        print(f"{'Q1收益':<12} {q1_return_alphalens:<12.4f} {q1_manual_mean:<12.4f} {abs(q1_return_alphalens - q1_manual_mean):<12.4f}")
        print(f"{'Q5收益':<12} {q5_return_alphalens:<12.4f} {q5_manual_mean:<12.4f} {abs(q5_return_alphalens - q5_manual_mean):<12.4f}")
        
        # 4. 逻辑一致性检查
        print("\n4️⃣ 逻辑一致性检查:")
        print("="*30)
        
        # alphalens逻辑
        alphalens_consistent = (ic_1d_alphalens < 0 and q1_return_alphalens > q5_return_alphalens) or \
                              (ic_1d_alphalens > 0 and q1_return_alphalens < q5_return_alphalens)
        
        # 手工计算逻辑
        manual_consistent = (ic_manual_mean < 0 and q1_manual_mean > q5_manual_mean) or \
                           (ic_manual_mean > 0 and q1_manual_mean < q5_manual_mean)
        
        print(f"alphalens逻辑一致: {'✅' if alphalens_consistent else '❌'}")
        print(f"手工计算逻辑一致: {'✅' if manual_consistent else '❌'}")
        
        if not alphalens_consistent:
            print(f"⚠️  alphalens存在逻辑矛盾!")
            print(f"   IC={ic_1d_alphalens:.4f} ({'负' if ic_1d_alphalens < 0 else '正'})")
            print(f"   Q1={q1_return_alphalens:.4f}, Q5={q5_return_alphalens:.4f}")
            print(f"   预期: {'Q1>Q5' if ic_1d_alphalens < 0 else 'Q5>Q1'}")
            print(f"   实际: {'Q1>Q5' if q1_return_alphalens > q5_return_alphalens else 'Q5>Q1'}")

def main():
    """主函数"""
    print("🎯 调试alphalens前向收益计算问题")
    print("="*50)
    
    # 1. 加载数据
    factor_data, price_data = load_sample_data()
    
    # 2. 准备alphalens数据
    factor_series, prices = prepare_alphalens_data(factor_data, price_data)
    
    # 3. 对比分析
    compare_alphalens_vs_manual(factor_series, prices)

if __name__ == "__main__":
    main()
