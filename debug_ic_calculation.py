#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试IC计算是否正确
"""

import sqlite3
import pandas as pd
import numpy as np
from datetime import datetime, timed<PERSON><PERSON>

def debug_ic_calculation():
    """调试IC计算"""
    print("🔍 调试IC计算")
    print("="*50)
    
    # 1. 获取一个样本期间的数据
    conn_factor = sqlite3.connect('ganggutong_factor_data.db')
    conn_price = sqlite3.connect('ganggutong_10year_data.db')
    
    # 获取2023年1月的数据作为样本
    factor_query = """
    SELECT stock_code, date, volatility_12m
    FROM volatility_12m_factor
    WHERE date BETWEEN '2023-01-01' AND '2023-01-31'
    AND volatility_12m IS NOT NULL
    ORDER BY date, stock_code
    """
    
    factor_data = pd.read_sql_query(factor_query, conn_factor)
    print(f"📊 因子数据: {len(factor_data)} 条")
    
    # 获取对应的价格数据
    stock_codes = factor_data['stock_code'].unique()
    stock_codes_str = "','".join(stock_codes)
    
    price_query = f"""
    SELECT stock_code, date, close
    FROM stock_prices
    WHERE stock_code IN ('{stock_codes_str}')
    AND date BETWEEN '2023-01-01' AND '2023-06-30'
    ORDER BY date, stock_code
    """
    
    price_data = pd.read_sql_query(price_query, conn_price)
    print(f"📊 价格数据: {len(price_data)} 条")
    
    conn_factor.close()
    conn_price.close()
    
    # 2. 手动计算IC
    # 选择一个特定日期
    test_date = '2023-01-03'
    
    # 获取该日期的因子值
    factor_on_date = factor_data[factor_data['date'] == test_date].copy()
    print(f"\n📅 {test_date} 的因子数据: {len(factor_on_date)} 只股票")
    
    if len(factor_on_date) == 0:
        print("❌ 没有找到该日期的因子数据")
        return
    
    # 计算60天后的收益率
    future_date = '2023-04-03'  # 大约60个交易日后
    
    # 获取当前价格
    current_prices = price_data[price_data['date'] == test_date][['stock_code', 'close']].copy()
    current_prices.columns = ['stock_code', 'current_price']
    
    # 获取未来价格
    future_prices = price_data[price_data['date'] == future_date][['stock_code', 'close']].copy()
    future_prices.columns = ['stock_code', 'future_price']
    
    # 合并数据
    merged_data = factor_on_date.merge(current_prices, on='stock_code', how='inner')
    merged_data = merged_data.merge(future_prices, on='stock_code', how='inner')
    
    # 计算收益率
    merged_data['return_60d'] = (merged_data['future_price'] / merged_data['current_price'] - 1)
    
    print(f"📈 成功匹配 {len(merged_data)} 只股票的收益率数据")
    
    if len(merged_data) < 10:
        print("❌ 数据太少，无法计算IC")
        return
    
    # 3. 计算IC
    ic = merged_data['volatility_12m'].corr(merged_data['return_60d'])
    print(f"\n📊 手动计算的IC: {ic:.4f}")

    # 4. 分析分位数表现
    merged_data['volatility_quantile'] = pd.qcut(merged_data['volatility_12m'], q=5, labels=[1,2,3,4,5])

    print(f"\n📊 各分位数表现:")
    quantile_performance = merged_data.groupby('volatility_quantile').agg({
        'volatility_12m': ['min', 'max', 'mean'],
        'return_60d': ['mean', 'count']
    }).round(4)

    print(quantile_performance)

    # 5. 检查是否符合IC的预期
    q1_return = merged_data[merged_data['volatility_quantile'] == 1]['return_60d'].mean()
    q5_return = merged_data[merged_data['volatility_quantile'] == 5]['return_60d'].mean()
    
    print(f"\n🎯 关键发现:")
    print(f"IC = {ic:.4f}")
    print(f"Q1(低波动率)平均收益: {q1_return:.4f}")
    print(f"Q5(高波动率)平均收益: {q5_return:.4f}")
    
    if ic < 0:
        print("✅ IC < 0，预期：Q1 > Q5")
        if q1_return > q5_return:
            print("✅ 结果符合预期：低波动率表现更好")
        else:
            print("❌ 结果不符合预期：高波动率表现更好")
    else:
        print("✅ IC > 0，预期：Q5 > Q1")
        if q5_return > q1_return:
            print("✅ 结果符合预期：高波动率表现更好")
        else:
            print("❌ 结果不符合预期：低波动率表现更好")
    
    # 6. 显示具体的股票例子
    print(f"\n📋 具体例子 (前10只股票):")
    sample_data = merged_data.head(10)[['stock_code', 'volatility_12m', 'return_60d', 'volatility_quantile']]
    print(sample_data.to_string(index=False))

if __name__ == "__main__":
    debug_ic_calculation()
